#!/usr/bin/env python3
"""
Патч для Router LiteLLM с поддержкой пользовательских стратегий маршрутизации.

Этот модуль патчит класс Router в LiteLLM для добавления поддержки
пользовательских стратегий маршрутизации, включая интеграцию с ML предиктором
для оптимизации выбора эндпоинтов.

## Что делает патч:
Перехватывает создание Router в LiteLLM и автоматически подключает пользовательскую
стратегию маршрутизации LLMTimePredictorRoutingStrategy, которая использует ML-предсказания
времени выполнения запросов для оптимального распределения нагрузки между развертываниями.

## Как работает патч:
1. Патчит метод Router.__init__ в LiteLLM
2. Обнаруживает необходимость кастомной стратегии из константы USE_CUSTOM_ROUTING
3. Временно инициализирует Router с простой стратегией (simple-shuffle)
4. После успешной инициализации подключает LLMTimePredictorRoutingStrategy
5. Использует официальный API LiteLLM set_custom_routing_strategy()
6. Устанавливает router.routing_strategy = "custom"

## Зачем нужен патч:
- Позволяет использовать продвинутую ML-основанную маршрутизацию без изменения кода LiteLLM
- Обеспечивает seamless интеграцию с существующими конфигурациями LiteLLM
- Автоматически активируется при USE_CUSTOM_ROUTING = True
- Предоставляет fallback на стандартные стратегии при ошибках
- Сохраняет совместимость с официальным API LiteLLM

## Преимущества:
- Неинвазивная интеграция - не требует патчей самого LiteLLM
- Автоматическое применение при импорте модуля
- Graceful degradation при недоступности компонентов
- Полная совместимость с экосистемой LiteLLM

Ключевые функции:
- Автоматическое обнаружение пользовательских стратегий маршрутизации
- Интеграция с фабрикой стратегий маршрутизации
- Поддержка аналитики и кеширования
- Патчинг LiteLLM для сохранения промптов в базе данных
"""

import functools
import os
from typing import Any, Dict, List, Optional
import yaml

from llm_router.logging_utils import get_logger
from llm_router.constants import (
    GPUSTACK_ENABLED,
    LOAD_BALANCING_WEIGHT,
    GPUSTACK_CACHE_TTL,
    DEFAULT_TASK_TYPE,
    USE_CUSTOM_ROUTING,
    create_routing_strategy_args,
)

logger = get_logger(__name__)


class StrategySetupError(Exception):
    """Исключение для ошибок настройки стратегии маршрутизации."""

    pass


def _validate_cache_compatibility(cache_instance: Any) -> None:
    """Проверяет совместимость кеша с пользовательской стратегией маршрутизации.

    Кастомная стратегия использует кеш для:
    - Хранения данных GPUStack (физические машины, модели)
    - Кеширования результатов ML предиктора
    - Хранения метрик least-busy мониторинга

    Поддерживаются различные типы кешей:
    - Redis (оптимально) - для production с несколькими инстансами
    - Локальный кеш - для development и single instance
    - Без кеша - работает, но с ограниченной функциональностью

    Args:
        cache_instance: Экземпляр кеша для проверки.

    Raises:
        StrategySetupError: Если кеш несовместим.
    """
    if cache_instance is None:
        logger.warning(
            "PATCH: Кеш не настроен, это может повлиять на производительность пользовательской стратегии"
        )
        return
    logger.debug(
        f"PATCH: Обнаружен кеш типа {type(cache_instance).__name__}, проверка совместимости"
    )
    # Проверяем тип кеша для оптимизации стратегии
    if hasattr(cache_instance, "redis_client"):
        logger.info(
            "PATCH: Обнаружен Redis кеш, совместим с пользовательской стратегией"
        )
    elif hasattr(cache_instance, "_cache"):
        logger.info(
            "PATCH: Обнаружен локальный кеш, ограниченная совместимость с пользовательской стратегией"
        )
    else:
        logger.warning(
            "PATCH: Неизвестный тип кеша, возможны проблемы с пользовательской стратегией"
        )


def _setup_custom_routing_strategy(router: Any) -> None:
    """Настраивает пользовательскую стратегию маршрутизации для роутера.

    Этот процесс выполняется ПОСЛЕ инициализации LiteLLM Router, когда
    все его внутренние компоненты уже настроены. Создается кастомная
    стратегия и заменяется встроенная стратегия LiteLLM.

    Процесс настройки:
    1. Проверяем совместимость кеша
    2. Создаем аргументы для стратегии на основе констант
    3. Создаем экземпляр LLMTimePredictorRoutingStrategy напрямую
    4. Устанавливаем стратегию через router.set_custom_routing_strategy()
    5. Указываем router.routing_strategy = "custom"
    6. Сохраняем ссылку в _custom_routing_strategy для последующих вызовов
    7. Запускаем background services для GPUStack cache manager

    Args:
        router: Экземпляр роутера LiteLLM.

    Raises:
        StrategySetupError: Если не удалось настроить стратегию.
    """
    logger.info("PATCH: Настройка экземпляра пользовательской стратегии маршрутизации")
    try:
        # Шаг 1: Проверяем совместимость кеша с кастомной стратегией
        _validate_cache_compatibility(getattr(router, "cache", None))

        # Шаг 2: Создаем аргументы для инициализации стратегии на основе констант
        routing_strategy_args = create_routing_strategy_args()

        # Логируем использование констант
        logger.info("CONFIG: Использование констант для настройки роутера:")
        logger.info(f"CONFIG: load_balancing_weight = {LOAD_BALANCING_WEIGHT}")
        logger.info(f"CONFIG: default_task_type = {DEFAULT_TASK_TYPE}")
        logger.info(f"CONFIG: gpustack_enabled = {GPUSTACK_ENABLED}")
        logger.info(f"CONFIG: gpustack_cache_ttl = {GPUSTACK_CACHE_TTL}")

        logger.debug(
            f"CONFIG: Аргументы для пользовательской стратегии: {routing_strategy_args}"
        )

        # Шаг 3: Создаем экземпляр LLMTimePredictorRoutingStrategy напрямую
        from llm_router.router.custom_router import LLMTimePredictorRoutingStrategy

        logger.debug("PATCH: Создание LLMTimePredictorRoutingStrategy")
        custom_strategy = LLMTimePredictorRoutingStrategy(
            router=router, routing_strategy_args=routing_strategy_args
        )
        logger.info("PATCH: LLMTimePredictorRoutingStrategy создана успешно")

        # Шаг 4: Устанавливаем кастомную стратегию через официальный API LiteLLM
        logger.debug("PATCH: Вызов set_custom_routing_strategy")
        router.set_custom_routing_strategy(custom_strategy)
        logger.debug("PATCH: set_custom_routing_strategy завершен")

        # Шаг 5: Указываем LiteLLM что используется кастомная стратегия
        router.routing_strategy = "custom"
        logger.info("PATCH: Routing strategy установлена как 'custom'")

        # Шаг 6: Сохраняем ссылку для повторных инициализаций
        setattr(router, "_custom_routing_strategy", custom_strategy)
        logger.info(
            "PATCH: Пользовательская стратегия маршрутизации настроена и активирована"
        )

        # Шаг 7: Запуск background services для GPUStack cache manager
        logger.debug("PATCH: Планирование запуска GPUStack background services")
        try:
            # Создаем задачу для запуска background services
            import asyncio

            asyncio.create_task(custom_strategy._start_background_services())
            logger.info("PATCH: Background services запланированы для запуска")
        except Exception as background_error:
            logger.warning(
                f"PATCH: Не удалось запланировать background services: {background_error}"
            )
    except ImportError as e:
        logger.error(
            f"PATCH: Не удалось импортировать LLMTimePredictorRoutingStrategy: {e}"
        )
        raise StrategySetupError(
            f"Не удалось импортировать LLMTimePredictorRoutingStrategy: {e}"
        )
    except Exception as e:
        logger.error(f"PATCH: Ошибка настройки пользовательской стратегии: {e}")
        raise StrategySetupError(f"Ошибка настройки пользовательской стратегии: {e}")


def patch_router_init():
    """
    Патчит метод __init__ класса Router для поддержки пользовательских стратегий.

    Добавляет логику для:
    - Обнаружения пользовательских стратегий маршрутизации
    - Настройки стратегий после инициализации роутера
    - Интеграции с системой аналитики

    Механизм работы:
    1. Сохраняем оригинальный Router.__init__
    2. Создаем wrapper функцию с дополнительной логикой
    3. Заменяем Router.__init__ на wrapper
    4. При создании Router автоматически применяется патч
    """
    try:
        from litellm import Router

        logger.info("PATCH: Импорт класса Router из LiteLLM выполнен успешно")
    except ImportError as e:
        logger.error(f"PATCH: Не удалось импортировать Router из LiteLLM: {e}")
        raise

    # Сохраняем оригинальный метод для последующего вызова
    original_router_init = Router.__init__
    logger.debug("PATCH: Сохранение оригинального Router.__init__")

    @functools.wraps(original_router_init)
    def patched_router_init(self, *args, **kwargs):
        """
        Патченый инициализатор роутера с поддержкой пользовательских стратегий.

        Последовательность выполнения:
        1. Проверка на повторную инициализацию (сохранение существующей стратегии)
        2. Временная замена routing_strategy на "simple-shuffle" для совместимости
        3. Вызов оригинального Router.__init__ для инициализации LiteLLM
        4. Патчинг функции сохранения промптов для аналитики
        5. Создание и установка кастомной стратегии через официальный API LiteLLM
        """
        logger.info("PATCH: Начало патченой инициализации Router")
        logger.debug(
            f"PATCH: Аргументы Router.__init__: args={len(args)}, kwargs keys={list(kwargs.keys())}"
        )

        # Шаг 1: Обработка повторной инициализации
        # Если роутер уже имеет кастомную стратегию, сохраняем её при повторной инициализации
        if hasattr(self, "_custom_routing_strategy"):
            if self._custom_routing_strategy is not None:
                original_custom_strategy = self._custom_routing_strategy
                logger.debug(
                    "PATCH: Обнаружена существующая пользовательская стратегия маршрутизации, повторная инициализация"
                )
                original_router_init(self, *args, **kwargs)
                self._custom_routing_strategy = original_custom_strategy
                logger.debug(
                    "PATCH: Пользовательская стратегия маршрутизации сохранена после повторной инициализации"
                )
            return

        # Шаг 2: Обработка routing_strategy для первичной инициализации
        routing_strategy = kwargs.get("routing_strategy", "simple-shuffle")
        logger.debug(
            f"CONFIG: Начальная routing_strategy из kwargs: {routing_strategy}"
        )

        # Если включена кастомная маршрутизация, временно используем simple-shuffle
        # для корректной инициализации LiteLLM, а затем заменим на кастомную стратегию
        if USE_CUSTOM_ROUTING:
            logger.info(
                "PATCH: Обнаружена пользовательская стратегия маршрутизации, будет настроена после инициализации"
            )
            # ВАЖНО: LiteLLM должен инициализироваться с валидной стратегией
            kwargs["routing_strategy"] = "simple-shuffle"
        else:
            logger.debug(
                f"PATCH: Пользовательская маршрутизация отключена, используем: {routing_strategy}"
            )

        # Шаг 3: Вызов оригинального инициализатора LiteLLM
        logger.debug("PATCH: Вызов оригинального Router.__init__")

        # Логируем callbacks перед инициализацией Router для отладки
        import litellm

        existing_callbacks = getattr(litellm, "callbacks", [])
        existing_service_callbacks = getattr(litellm, "service_callback", [])
        logger.debug(
            f"PATCH: Callbacks перед инициализацией Router: {existing_callbacks}"
        )
        logger.debug(
            f"PATCH: Service callbacks перед инициализацией Router: {existing_service_callbacks}"
        )

        original_router_init(self, *args, **kwargs)
        logger.debug("PATCH: Оригинальный Router.__init__ завершен")

        # Шаг 4: Патчинг функции сохранения промптов для аналитики
        # Это нужно для того чтобы промпты сохранялись в БД для анализа
        try:
            import litellm.proxy.spend_tracking.spend_tracking_utils

            def _patched_should_store_prompts_and_responses_in_spend_logs() -> bool:
                """
                Патченая версия функции - всегда возвращает True для сохранения промптов и ответов.

                Оригинальная функция может возвращать False в зависимости от настроек,
                но промпты нужны для аналитики маршрутизации, поэтому принудительно
                включаем сохранение.
                """
                return True

            # Заменяем функцию на патченую версию
            litellm.proxy.spend_tracking.spend_tracking_utils._should_store_prompts_and_responses_in_spend_logs = (
                _patched_should_store_prompts_and_responses_in_spend_logs
            )
            result = (
                litellm.proxy.spend_tracking.spend_tracking_utils._should_store_prompts_and_responses_in_spend_logs()
            )
            logger.info(
                f"PATCH: Функция _should_store_prompts_and_responses_in_spend_logs пропатчена, результат: {result}"
            )
        except Exception as e:
            logger.warning(
                f"PATCH: Не удалось пропатчить функцию сохранения промптов: {e}"
            )

        # Шаг 5: Настройка кастомной стратегии маршрутизации через официальный API LiteLLM
        if USE_CUSTOM_ROUTING:
            try:
                logger.info("PATCH: Настройка пользовательской стратегии маршрутизации")
                _setup_custom_routing_strategy(self)
                logger.info(
                    "PATCH: Пользовательская стратегия маршрутизации настроена успешно"
                )
            except StrategySetupError as e:
                logger.error(f"PATCH: Ошибка настройки пользовательской стратегии: {e}")
                # Не пробрасываем исключение - роутер должен работать с базовой стратегией
            except Exception as e:
                logger.error(f"PATCH: Неожиданная ошибка при настройке стратегии: {e}")
        else:
            logger.debug("PATCH: Пользовательская стратегия маршрутизации не требуется")

        logger.info("PATCH: Инициализация роутера завершена")

    # Заменяем оригинальный __init__ на патченый
    Router.__init__ = patched_router_init
    logger.info("PATCH: Router.__init__ успешно пропатчен")


def patch_license_init():
    """
    Патчит проверку лицензии для использования кастомного LicenseCheck.

    Проблема: _license_check и premium_user инициализируются при импорте proxy_server.py,
    но кастомный файл лицензии копируется в контейнер позже. Нужно принудительно
    обновить эти переменные после применения патча.
    """
    logger.info("PATCH: Обновление проверки лицензии")
    try:
        # Импортируем кастомный LicenseCheck из нашего файла
        import sys
        import importlib.util

        # Добавляем путь к нашему файлу лицензии в sys.path если его там нет
        license_paths = [
            "/app",
            "/usr/local/lib/python3.13/site-packages",
            "/usr/lib/python3.13/site-packages",
        ]
        for path in license_paths:
            if path not in sys.path:
                sys.path.insert(0, path)

        # Пытаемся импортировать кастомный LicenseCheck
        try:
            from license import LicenseCheck as CustomLicenseCheck

            logger.info("PATCH: Кастомный LicenseCheck импортирован успешно")

            # Обновляем глобальные переменные в proxy_server
            import litellm.proxy.proxy_server as proxy_server

            # Создаем новый экземпляр кастомного LicenseCheck
            proxy_server._license_check = CustomLicenseCheck()
            proxy_server.premium_user = proxy_server._license_check.is_premium()

            logger.info(f"PATCH: premium_user обновлен на: {proxy_server.premium_user}")
            logger.info(
                f"PATCH: _license_check.is_premium(): {proxy_server._license_check.is_premium()}"
            )

        except ImportError as e:
            logger.warning(
                f"PATCH: Не удалось импортировать кастомный LicenseCheck: {e}"
            )
            logger.info("PATCH: Используется стандартная проверка лицензии LiteLLM")

    except Exception as e:
        logger.error(f"PATCH: Ошибка обновления проверки лицензии: {e}")


def patch_metrics_init():
    """
    Патчит настройку метрик LLM Router.

    Настраивает метрики ПЕРЕД применением основных патчей.
    ВАЖНО: Метрики должны быть настроены ДО создания Router, потому что
    Router добавляется к существующим litellm.callbacks, а не заменяет их.
    """
    logger.info("PATCH: Настройка метрик LLM Router")
    try:
        from llm_router.config.metrics_config import setup_llm_router_metrics

        metrics_result = setup_llm_router_metrics()
        if metrics_result.get("prometheus_metrics") or metrics_result.get(
            "system_metrics"
        ):
            logger.info("PATCH: Метрики LLM Router успешно настроены")
            
            # Принудительно монтируем эндпоинт /metrics
            _force_mount_metrics_endpoint()
        else:
            logger.warning("PATCH: Метрики LLM Router не настроены")
    except Exception as e:
        logger.warning(f"PATCH: Ошибка настройки метрик (не критично): {e}")


def _force_mount_metrics_endpoint():
    """
    Принудительно монтирует эндпоинт /metrics для Prometheus.
    
    Обходит проверку premium_user в LiteLLM и напрямую монтирует эндпоинт.
    """
    logger.info("PATCH: Принудительное монтирование эндпоинта /metrics")
    try:
        from litellm.integrations.prometheus import PrometheusLogger
        from prometheus_client import make_asgi_app
        
        # Импортируем app из proxy_server
        from litellm.proxy.proxy_server import app
        
        # Проверяем, не смонтирован ли уже эндпоинт
        existing_routes = [route.path for route in app.routes if hasattr(route, 'path')]
        if "/metrics" in existing_routes:
            logger.info("PATCH: Эндпоинт /metrics уже существует")
            return
            
        # Создаем metrics ASGI app
        metrics_app = make_asgi_app()
        
        # Монтируем эндпоинт
        app.mount("/metrics", metrics_app)
        logger.info("PATCH: Эндпоинт /metrics успешно смонтирован")
        
        # Вызываем оригинальный метод для логирования
        try:
            PrometheusLogger._mount_metrics_endpoint(True)  # premium_user=True
        except Exception as e:
            logger.debug(f"PATCH: Ошибка вызова оригинального метода (не критично): {e}")
            
    except ImportError as e:
        logger.error(f"PATCH: Не удалось импортировать компоненты Prometheus: {e}")
    except Exception as e:
        logger.error(f"PATCH: Ошибка монтирования эндпоинта /metrics: {e}")


def apply_patch():
    """
    Применяет все патчи для LiteLLM.

    Эта функция вызывается из start_litellm_with_router.py для применения
    патчей перед запуском LiteLLM прокси сервера.

    Включает:
    - Обновление проверки лицензии для использования кастомного LicenseCheck
    - Настройка метрик LLM Router
    - Патч инициализации Router для пользовательских стратегий
    - Патч функций сохранения данных для аналитики

    Returns:
        bool: True если патчи применены успешно
    """
    logger.info("PATCH: Начало применения патчей LiteLLM")
    try:
        # Обновление проверки лицензии ПЕРЕД всеми остальными патчами
        patch_license_init()

        # Настройка метрик ПЕРЕД применением основных патчей
        patch_metrics_init()

        # Применение основных патчей ПОСЛЕ настройки метрик
        patch_router_init()
        logger.info("PATCH: Все патчи LiteLLM применены успешно")
        return True
    except Exception as e:
        logger.error(f"PATCH: Ошибка применения патчей LiteLLM: {e}")
        raise
