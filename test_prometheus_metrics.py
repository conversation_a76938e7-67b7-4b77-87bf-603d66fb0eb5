#!/usr/bin/env python3
"""
Тест для проверки работы метрик Prometheus в LiteLLM Router.

Этот скрипт проверяет:
1. Доступность эндпоинта /metrics
2. Наличие основных метрик LiteLLM
3. Отсутствие предупреждений о premium user
"""

import requests
import sys
import time


def test_metrics_endpoint():
    """Тестирует доступность эндпоинта /metrics."""
    print("🔍 Тестирование эндпоинта /metrics...")
    
    try:
        response = requests.get("http://localhost:4000/metrics/", timeout=10)
        if response.status_code == 200:
            print("✅ Эндпоинт /metrics доступен")
            return response.text
        else:
            print(f"❌ Эндпоинт /metrics недоступен. Статус: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Ошибка подключения к эндпоинту /metrics: {e}")
        return None


def test_litellm_metrics(metrics_text):
    """Проверяет наличие основных метрик LiteLLM."""
    print("🔍 Проверка наличия метрик LiteLLM...")
    
    expected_metrics = [
        "litellm_total_tokens_total",
        # Другие метрики могут появиться после запросов
    ]
    
    found_metrics = []
    missing_metrics = []
    
    for metric in expected_metrics:
        if metric in metrics_text:
            found_metrics.append(metric)
            print(f"✅ Найдена метрика: {metric}")
        else:
            missing_metrics.append(metric)
            print(f"❌ Отсутствует метрика: {metric}")
    
    # Проверяем общее количество метрик LiteLLM
    litellm_metrics_count = metrics_text.count("litellm_")
    print(f"📊 Всего найдено метрик LiteLLM: {litellm_metrics_count}")
    
    if litellm_metrics_count > 0:
        print("✅ Метрики LiteLLM присутствуют")
        return True
    else:
        print("❌ Метрики LiteLLM отсутствуют")
        return False


def test_no_premium_warnings():
    """Проверяет отсутствие предупреждений о premium user в логах."""
    print("🔍 Проверка отсутствия предупреждений о premium user...")
    
    try:
        import subprocess
        result = subprocess.run(
            ["docker", "logs", "filin-litellm-router", "--since", "2m"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        logs = result.stdout + result.stderr
        premium_warnings = [line for line in logs.split('\n') if '🚨' in line and 'Prometheus' in line]
        
        if premium_warnings:
            print("❌ Найдены предупреждения о premium user:")
            for warning in premium_warnings[-3:]:  # Показываем последние 3
                print(f"  {warning}")
            return False
        else:
            print("✅ Предупреждения о premium user отсутствуют")
            return True
            
    except Exception as e:
        print(f"⚠️  Не удалось проверить логи: {e}")
        return True  # Не считаем это критической ошибкой


def main():
    """Основная функция тестирования."""
    print("🚀 Запуск тестов метрик Prometheus для LiteLLM Router")
    print("=" * 60)
    
    # Тест 1: Доступность эндпоинта
    metrics_text = test_metrics_endpoint()
    if not metrics_text:
        print("❌ Критическая ошибка: эндпоинт /metrics недоступен")
        sys.exit(1)
    
    print()
    
    # Тест 2: Наличие метрик LiteLLM
    metrics_ok = test_litellm_metrics(metrics_text)
    
    print()
    
    # Тест 3: Отсутствие предупреждений
    no_warnings = test_no_premium_warnings()
    
    print()
    print("=" * 60)
    
    if metrics_ok and no_warnings:
        print("🎉 Все тесты пройдены успешно!")
        print("✅ Метрики Prometheus работают корректно")
        sys.exit(0)
    else:
        print("❌ Некоторые тесты не пройдены")
        sys.exit(1)


if __name__ == "__main__":
    main()
