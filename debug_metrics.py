#!/usr/bin/env python3
"""
Скрипт диагностики метрик LiteLLM
"""

import requests
import os

def get_auth_headers():
    """Получение заголовков аутентификации"""
    master_key = os.getenv("LITELLM_MASTER_KEY", "sk-1234")
    return {"Authorization": f"Bearer {master_key}"}

def check_metrics_endpoint():
    """Проверка доступности эндпоинта метрик"""
    base_url = os.getenv("LITELLM_API_BASE", "http://localhost:4000")
    metrics_url = f"{base_url.rstrip('/')}/metrics"
    
    print(f"Проверка эндпоинта: {metrics_url}")
    
    try:
        # Сначала пробуем без аутентификации
        response = requests.get(metrics_url, timeout=5)
        if response.status_code == 200:
            print("OK: Эндпоинт метрик доступен без аутентификации")
            print(f"Содержимое (первые 500 символов):")
            print(response.text[:500])
            return True
        elif response.status_code == 401:
            print("INFO: Эндпоинт требует аутентификацию, пробуем с ключом")
            # Пробуем с аутентификацией
            auth_response = requests.get(metrics_url, headers=get_auth_headers(), timeout=5)
            if auth_response.status_code == 200:
                print("OK: Эндпоинт метрик доступен с аутентификацией")
                print(f"Содержимое (первые 500 символов):")
                print(auth_response.text[:500])
                return True
            else:
                print(f"ERROR: Эндпоинт с аутентификацией вернул статус: {auth_response.status_code}")
                print(f"Ответ: {auth_response.text[:200]}")
                return False
        else:
            print(f"ERROR: Эндпоинт вернул статус: {response.status_code}")
            print(f"Ответ: {response.text[:200]}")
            return False
    except requests.exceptions.ConnectionError:
        print("ERROR: Не удалось подключиться к серверу")
        print("INFO: Убедитесь что LiteLLM сервер запущен")
        return False
    except requests.exceptions.Timeout:
        print("ERROR: Таймаут при обращении к серверу")
        return False
    except Exception as e:
        print(f"ERROR: Ошибка: {e}")
        return False

def check_environment():
    """Проверка переменных окружения"""
    print("\nПроверка переменных окружения:")
    
    env_vars = {
        "LITELLM_API_BASE": os.getenv("LITELLM_API_BASE", "http://localhost:4000"),
        "LITELLM_MASTER_KEY": os.getenv("LITELLM_MASTER_KEY", "sk-1234"),
        "LLM_ROUTER_PROMETHEUS_METRICS": os.getenv("LLM_ROUTER_PROMETHEUS_METRICS", "true"),
        "LLM_ROUTER_SYSTEM_METRICS": os.getenv("LLM_ROUTER_SYSTEM_METRICS", "true"),
    }
    
    for var, value in env_vars.items():
        if var == "LITELLM_MASTER_KEY":
            # Не показываем полный ключ
            masked_value = f"{value[:8]}..." if len(value) > 8 else "***"
            print(f"  {var}: {masked_value}")
        else:
            print(f"  {var}: {value}")
    
    return True

def check_litellm_config():
    """Проверка конфигурации LiteLLM"""
    print("\nПроверка конфигурации LiteLLM:")
    
    try:
        import litellm
        
        callbacks = getattr(litellm, "callbacks", [])
        service_callbacks = getattr(litellm, "service_callback", [])
        
        print(f"litellm.callbacks: {callbacks}")
        print(f"litellm.service_callback: {service_callbacks}")
        
        has_prometheus = "prometheus" in callbacks
        has_system = "prometheus_system" in service_callbacks
        
        print(f"Prometheus callbacks: {'OK' if has_prometheus else 'DISABLED'}")
        print(f"System metrics: {'OK' if has_system else 'DISABLED'}")
        
        if not has_prometheus and not has_system:
            print("WARNING: Метрики не настроены в LiteLLM!")
            return False
            
        return has_prometheus or has_system
        
    except ImportError:
        print("ERROR: LiteLLM не импортируется")
        return False
    except Exception as e:
        print(f"ERROR: Ошибка проверки конфигурации: {e}")
        return False
    """Проверка конфигурации LiteLLM"""
    print("\nПроверка конфигурации LiteLLM:")
    
    try:
        import litellm
        
        callbacks = getattr(litellm, "callbacks", [])
        service_callbacks = getattr(litellm, "service_callback", [])
        
        print(f"litellm.callbacks: {callbacks}")
        print(f"litellm.service_callback: {service_callbacks}")
        
        has_prometheus = "prometheus" in callbacks
        has_system = "prometheus_system" in service_callbacks
        
        print(f"Prometheus callbacks: {'OK' if has_prometheus else 'DISABLED'}")
        print(f"System metrics: {'OK' if has_system else 'DISABLED'}")
        
        if not has_prometheus and not has_system:
            print("WARNING: Метрики не настроены в LiteLLM!")
            return False
            
        return has_prometheus or has_system
        
    except ImportError:
        print("ERROR: LiteLLM не импортируется")
        return False
    except Exception as e:
        print(f"ERROR: Ошибка проверки конфигурации: {e}")
        return False

def check_server_health():
    """Проверка работоспособности сервера"""
    base_url = os.getenv("LITELLM_API_BASE", "http://localhost:4000")
    health_url = f"{base_url.rstrip('/')}/health"
    
    print(f"\nПроверка работоспособности сервера: {health_url}")
    
    try:
        # Сначала пробуем без аутентификации
        response = requests.get(health_url, timeout=5)
        if response.status_code == 200:
            print("OK: Сервер работает без аутентификации")
            return True
        elif response.status_code == 401:
            print("INFO: Сервер требует аутентификацию, пробуем с ключом")
            # Пробуем с аутентификацией
            auth_response = requests.get(health_url, headers=get_auth_headers(), timeout=5)
            if auth_response.status_code == 200:
                print("OK: Сервер работает с аутентификацией")
                return True
            else:
                print(f"ERROR: Сервер с аутентификацией вернул статус: {auth_response.status_code}")
                return False
        else:
            print(f"ERROR: Сервер вернул статус: {response.status_code}")
            return False
    except Exception as e:
        print(f"ERROR: Сервер недоступен: {e}")
        return False

def main():
    """Основная функция диагностики"""
    print("Диагностика метрик LiteLLM")
    print("=" * 50)
    
    # Проверяем переменные окружения
    env_ok = check_environment()
    
    # Проверяем сервер
    server_ok = check_server_health()
    
    # Проверяем конфигурацию
    config_ok = check_litellm_config()
    
    # Проверяем эндпоинт метрик
    metrics_ok = check_metrics_endpoint()
    
    print("\n" + "=" * 50)
    print("Результат диагностики:")
    print(f"   Окружение: {'OK' if env_ok else 'FAIL'}")
    print(f"   Сервер: {'OK' if server_ok else 'FAIL'}")
    print(f"   Конфигурация: {'OK' if config_ok else 'FAIL'}")
    print(f"   Эндпоинт метрик: {'OK' if metrics_ok else 'FAIL'}")
    
    if not server_ok:
        print("\nРекомендации:")
        print("   1. Запустите LiteLLM сервер")
        print("   2. Проверьте переменную LITELLM_API_BASE")
    elif not config_ok:
        print("\nРекомендации:")
        print("   1. Проверьте применение патчей метрик")
        print("   2. Убедитесь что вызывается patch_metrics_init()")
        print("   3. Проверьте переменные окружения LLM_ROUTER_PROMETHEUS_METRICS")
    elif not metrics_ok:
        print("\nРекомендации:")
        print("   1. Метрики настроены, но эндпоинт недоступен")
        print("   2. Проверьте версию LiteLLM")
        print("   3. Проверьте логи сервера")

if __name__ == "__main__":
    main()
